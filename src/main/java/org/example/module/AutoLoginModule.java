package org.example.module;

import com.github.rfresh2.EventConsumer;
import com.zenith.Proxy;
import com.zenith.event.chat.SystemChatEvent;
import com.zenith.event.client.ClientConnectEvent;
import com.zenith.event.client.ClientDisconnectEvent;
import com.zenith.module.api.Module;
import com.zenith.util.timer.Timer;
import com.zenith.util.timer.Timers;
import org.example.AutoLoginPlugin;
import org.geysermc.mcprotocollib.protocol.packet.ingame.serverbound.ServerboundChatPacket;

import java.util.List;

import static com.github.rfresh2.EventConsumer.of;
import static com.zenith.Globals.CONFIG;

public class AutoLoginModule extends Module {
    final Timer timer = Timers.tickTimer();
    private static final String LOGIN_COMMAND = "/login";

    public boolean isOnline = false;

    @Override
    public boolean enabledSetting() {
        return AutoLoginPlugin.PLUGIN_CONFIG.enabled;
    }

    @Override
    public List<EventConsumer<?>> registerEvents() {
        return List.of(
                of(ClientDisconnectEvent.class, this::handleDisconnectEvent),
                of(ClientConnectEvent.class, this::handleConnectEvent),
                of(SystemChatEvent.class, this::handleLogin)
        );
    }

    private void handleConnectEvent(ClientConnectEvent clientConnectEvent) {

    }

    private void handleDisconnectEvent(ClientDisconnectEvent clientDisconnectEvent) {

        info("客户端断开连接:" + clientDisconnectEvent.reason());

        isOnline = false;
    }

    public boolean getOnline() {
        boolean connected = Proxy.getInstance().isConnected();

        return isOnline && connected;
    }
    public void reboot() {
       Proxy.getInstance().disconnect();
       Proxy.getInstance().stop();
    }

    private void handleLogin(SystemChatEvent event) {
        if (!AutoLoginPlugin.PLUGIN_CONFIG.enabled) {
            return;
        }

        String message = event.message();
        if (message.contains("/login")) {
            info("自动登录中...: {}", CONFIG.authentication.username);

            sendChatMessage(LOGIN_COMMAND + " " + CONFIG.authentication.password);
        }

        // 检查登录是否成功
        if (message.contains("Connecting to the server")) {
            info("bot 进入游戏服中");
            isOnline = true;
        }

        // 检查是否被踢出
        if (message.contains("kicked") || message.contains("disconnected")) {
            warn("bot被服务器踢出");
            isOnline = false;
        }
        //一一一一一一一一一一一一一一一一一一一一一一一
        //This server is restarting in 1m at 05:00 !
        //You were kicked from server: Server restarting, please wait a minute
        //You were kicked from login: A player with the same IP is already in game!

        if (message.contains("This server is restarting in")) {
            info("检测到服务器重启，准备. 自动重启中...: {}");

        }
    }

    private void sendChatMessage(String message) {
        if (Proxy.getInstance().isConnected()) {
            Proxy.getInstance().getClient().sendAsync(new ServerboundChatPacket(message));
            info("发送login消息: {}", message);
        }
    }

}

package org.example.module;

import com.github.rfresh2.EventConsumer;
import com.zenith.Proxy;
import com.zenith.event.chat.SystemChatEvent;
import com.zenith.event.client.ClientConnectEvent;
import com.zenith.event.client.ClientDisconnectEvent;
import com.zenith.module.api.Module;
import com.zenith.util.timer.Timer;
import com.zenith.util.timer.Timers;
import org.example.AutoLoginPlugin;
import org.geysermc.mcprotocollib.protocol.packet.ingame.serverbound.ServerboundChatPacket;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

import static com.github.rfresh2.EventConsumer.of;
import static com.zenith.Globals.CONFIG;

public class AutoLoginModule extends Module {
    final Timer timer = Timers.tickTimer();
    private static final String LOGIN_COMMAND = "/login";

    // 定时任务相关
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private ScheduledFuture<?> rebootTask;

    // 防抖相关
    private long lastRestartMessageTime = 0;
    private static final long DEBOUNCE_INTERVAL = 5000; // 5秒防抖间隔

    public boolean isOnline = false;

    @Override
    public boolean enabledSetting() {
        return AutoLoginPlugin.PLUGIN_CONFIG.enabled;
    }

    @Override
    public List<EventConsumer<?>> registerEvents() {
        return List.of(
                of(ClientDisconnectEvent.class, this::handleDisconnectEvent),
                of(ClientConnectEvent.class, this::handleConnectEvent),
                of(SystemChatEvent.class, this::handleLogin)
        );
    }

    private void handleConnectEvent(ClientConnectEvent clientConnectEvent) {
        info("客户端连接成功，清理重启任务");
        clearRebootTasks();
    }

    private void handleDisconnectEvent(ClientDisconnectEvent clientDisconnectEvent) {
        info("客户端断开连接:" + clientDisconnectEvent.reason());
        isOnline = false;
    }

    public boolean getOnline() {
        boolean connected = Proxy.getInstance().isConnected();

        return isOnline && connected;
    }
    public void reboot() {
       Proxy.getInstance().disconnect();
       Proxy.getInstance().stop();
    }

    private void handleLogin(SystemChatEvent event) {
        if (!AutoLoginPlugin.PLUGIN_CONFIG.enabled) {
            return;
        }

        String message = event.message();
        if (message.contains("/login")) {
            info("自动登录中...: {}", CONFIG.authentication.username);

            sendChatMessage(LOGIN_COMMAND + " " + CONFIG.authentication.password);
        }

        // 检查登录是否成功
        if (message.contains("Connecting to the server")) {
            info("bot 进入游戏服中");
            isOnline = true;
        }

        // 检查是否被踢出
        if (message.contains("kicked") || message.contains("disconnected")) {
            warn("bot被服务器踢出");
            isOnline = false;
        }
        //一一一一一一一一一一一一一一一一一一一一一一一
        //This server is restarting in 1m at 05:00 !
        //You were kicked from server: Server restarting, please wait a minute
        //You were kicked from login: A player with the same IP is already in game!

        if (message.contains("This server is restarting in")) {
            handleServerRestart(message);
        }
    }

    private void sendChatMessage(String message) {
        if (Proxy.getInstance().isConnected()) {
            Proxy.getInstance().getClient().sendAsync(new ServerboundChatPacket(message));
            info("发送login消息: {}", message);
        }
    }

    /**
     * 处理服务器重启消息
     */
    private void handleServerRestart(String message) {
        long currentTime = System.currentTimeMillis();

        // 防抖：如果距离上次处理重启消息不足5秒，则忽略
        if (currentTime - lastRestartMessageTime < DEBOUNCE_INTERVAL) {
            info("重启消息防抖，忽略重复消息");
            return;
        }

        lastRestartMessageTime = currentTime;
        info("检测到服务器重启消息: {}", message);

        // 取消现有的重启任务
        clearRebootTasks();

        // 设置目标时间：当前时间 + 2分钟
        long initialDelay = 2 * 60; // 2分钟，单位：秒
        long period = 3 * 60; // 3分钟间隔，单位：秒

        info("开始设置重启任务：{}秒后开始，每{}秒重试一次", initialDelay, period);

        // 创建定时重启任务
        rebootTask = scheduler.scheduleAtFixedRate(() -> {
            try {
                if (getOnline()) {
                    info("检测到已重新在线，取消重启任务");
                    clearRebootTasks();
                    return;
                }

                info("执行自动重启...");
                reboot();
            } catch (Exception e) {
                warn("重启任务执行异常: {}", e.getMessage());
            }
        }, initialDelay, period, TimeUnit.SECONDS);

        info("重启任务已设置");
    }

    /**
     * 清空所有重启任务
     */
    private void clearRebootTasks() {
        if (rebootTask != null && !rebootTask.isCancelled()) {
            rebootTask.cancel(false);
            info("已取消重启任务");
        }
        rebootTask = null;
    }

    /**
     * 模块关闭时清理资源
     */
    @Override
    public void onDisable() {
        clearRebootTasks();
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        super.onDisable();
    }

}
